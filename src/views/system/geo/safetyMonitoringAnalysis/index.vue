<script setup>
import { onMounted, onUnmounted, nextTick, reactive, ref } from "vue";
import * as echarts from "echarts";
import {
  getSafetyMonitorPointInfo,
  getSafetyMonitorTree,
} from "@/api/watershed/forecast/index.js";
import moment from "moment";

defineOptions({
  name: "TownshipIndex",
});

const { proxy } = getCurrentInstance();
const accuracyList = ref([]);
const total = ref(0);
const loading = ref(false);
const refreshTable = ref(true);

// 分页处理
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  forecastSchemeId: undefined,
  status: 1,
});

const indexMethod = (index) => {
  return (queryParams.pageNum - 1) * queryParams.pageSize + index + 1;
};

// 左侧树过滤相关
const filterText = ref("");
const treeRef = ref(null);

const treeData = ref([]);

// 获取树列表
const getSafetyMonitorTreeList = async () => {
  const res = await getSafetyMonitorTree({
    name: filterText.value,
  });
  if (res.code === 200) {
    treeData.value = res.rows || [];
  }
};

// 判断是否为三级节点（叶子节点）
const isThirdLevelNode = (data) => {
  // 有 nodeCode 值且 children 为 null 或空数组
  return data.nodeCode && (!data.children || data.children.length === 0);
};

const checkedNodes = ref([]);
// 处理节点选中状态变化
const handleNodeCheckChange = (data, checked) => {
  // 只有三级节点才能被选中
  if (!isThirdLevelNode(data)) {
    return;
  }

  // 设置节点的选中状态
  treeRef.value.setChecked(data.nodeCode, checked, false);

  // 获取所有选中的三级节点

  checkedNodes.value = treeRef.value
    .getCheckedNodes()
    .filter((node) => isThirdLevelNode(node));
  console.log(checkedNodes.value, "checkedNodes.value");
};

// 图表相关
const chartRef = ref(null);
let chartInstance = null;

// 日期时间选择器相关
const dateTimeRange = ref([]);
const dateTimePickerDisabledDate = (time) => {
  // 禁用当前时间之后的时间
  return time.getTime() > Date.now();
};

// 初始化日期时间范围（最近7天）
const initDateTimeRange = () => {
  const start = moment().subtract(7, "days").format("YYYY-MM-DD HH:mm");
  const end = moment().format("YYYY-MM-DD HH:mm");
  dateTimeRange.value = [start, end];
};

// mock数据
const chartData = [
  { time: "2024-06-01 10:00", pressure: 120, rainfall: 10 },
  { time: "2024-06-01 11:00", pressure: 130, rainfall: 15 },
  { time: "2024-06-01 12:00", pressure: 110, rainfall: 8 },
  { time: "2024-06-01 13:00", pressure: 140, rainfall: 0 },
  { time: "2024-06-01 14:00", pressure: 135, rainfall: 5 },
];
const renderChart = () => {
  nextTick(() => {
    if (!chartRef.value) return;
    if (!chartInstance) {
      chartInstance = echarts.init(chartRef.value);
    }
    const xData = chartData.map((item) => item.time);
    const pressureData = chartData.map((item) => item.pressure);
    const rainfallData = chartData.map((item) => item.rainfall);
    chartInstance.setOption({
      tooltip: { trigger: "axis" },
      legend: { data: ["渗压(kpa)", "降雨量(mm)"] },
      grid: { left: 50, right: 50, top: 30, bottom: 40 },
      xAxis: [
        {
          type: "category",
          data: xData,
          axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: "auto" },
          name: "时间",
        },
      ],
      yAxis: [
        { type: "value", name: "渗压(kpa)", min: 0 },
        { type: "value", name: "降雨量(mm)", min: 0, position: "right" },
      ],
      series: [
        {
          name: "渗压(kpa)",
          type: "line",
          data: pressureData,
          yAxisIndex: 0,
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          lineStyle: { width: 2 },
          itemStyle: { color: "#409EFF" },
        },
        {
          name: "降雨量(mm)",
          type: "line",
          data: rainfallData,
          yAxisIndex: 1,
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          lineStyle: { width: 2 },
          itemStyle: { color: "#E6A23C" },
        },
      ],
    });
    chartInstance.resize();
  });
};
const handleChartResize = () => {
  if (chartInstance) chartInstance.resize();
};

const handleSearch = async () => {
  try {
    if (checkedNodes.value.length === 0) {
      proxy.$modal.msgWarning("请选择监测点");
      return;
    }
    if (dateTimeRange.value.length === 0) {
      proxy.$modal.msgWarning("请选择时间");
      return;
    }
    loading.value = true;
    const res = await getSafetyMonitorPointInfo({
      list: checkedNodes.value.map((item) => {
        return {
          nodeCode: item.nodeCode,
        };
      }),
      startTime: dateTimeRange.value[0],
      endTime: dateTimeRange.value[1],
    });
    if (res.code === 200) {
      accuracyList.value = res.rows;
      total.value = res.total || 0;
    }
  } catch (e) {
    console.log(e);
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  // 重置日期时间范围到最近7天
  initDateTimeRange();
  // 清空选中的节点
  if (treeRef.value) {
    treeRef.value.setCheckedKeys([]);
  }
  checkedNodes.value = [];
};

onMounted(async () => {
  getSafetyMonitorTreeList();
  initDateTimeRange();
  renderChart();
  window.addEventListener("resize", handleChartResize);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", handleChartResize);
});
</script>

<template>
  <div class="app-container">
    <div class="analysis-flex-container">
      <!-- 左侧区域 -->
      <div class="analysis-flex-left">
        <el-row :gutter="8" style="margin-bottom: 12px">
          <el-col :span="17">
            <el-input
              v-model="filterText"
              placeholder="输入关键字过滤"
              size="small"
              clearable
            />
          </el-col>
          <el-col :span="7">
            <el-button
              type="primary"
              size="small"
              @click="getSafetyMonitorTreeList"
              icon="Search"
              >查询</el-button
            >
          </el-col>
        </el-row>
        <el-tree
          ref="treeRef"
          :data="treeData"
          node-key="nodeCode"
          :props="{
            label: 'nodeName',
            value: 'nodeCode',
            children: 'children',
          }"
          check-strictly
          default-expand-all
          style="flex: 1; overflow: auto"
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span v-if="isThirdLevelNode(data)" class="checkbox-container">
                <el-checkbox
                  :model-value="node.checked"
                  @change="(checked) => handleNodeCheckChange(data, checked)"
                ></el-checkbox>
              </span>
              <span>{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </div>
      <div class="analysis-flex-right">
        <!-- 日期时间选择器 -->
        <div class="chart-header">
          <el-form inline>
            <el-form-item>
              <el-date-picker
                v-model="dateTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                :disabled-date="dateTimePickerDisabledDate"
                :shortcuts="[
                  {
                    text: '最近7天',
                    value: () => {
                      const end = new Date();
                      const start = new Date();
                      start.setTime(start.getTime() - 7 * 24 * 60 * 60 * 1000);
                      return [start, end];
                    },
                  },
                  {
                    text: '最近30天',
                    value: () => {
                      const end = new Date();
                      const start = new Date();
                      start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000);
                      return [start, end];
                    },
                  },
                ]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" icon="Search"
                >查询</el-button
              >
              <el-button type="primary" plain @click="handleReset" icon="Refresh"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div ref="chartRef" class="chart-container" />
        <el-table
          v-if="refreshTable"
          v-loading="loading"
          :data="accuracyList"
          row-key="id"
          stripe
          class="flex-half-table"
        >
          <el-table-column
            type="index"
            label="序号"
            width="65"
            align="center"
            :index="indexMethod"
          ></el-table-column>
          <el-table-column
            prop="schemeName"
            label="时间"
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="floodHistoryCode"
            label="降雨量(mm)"
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="basinName"
            label="渗压(kPa)"
            align="center"
          ></el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.rainfall-flex-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 420px;
  min-height: 320px;
  margin-top: 10px;
}
.rainfall-table-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 6px 0 0 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 16px 8px 16px 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.rainfall-divider {
  width: 2px;
  background: #e5e6eb;
  margin: 0 8px;
  border-radius: 2px;
  height: 100%;
  align-self: stretch;
}
.rainfall-chart-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 16px 16px 16px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.rainfall-echart {
  width: 100%;
  height: 100%;
  min-height: 320px;
}
.analysis-flex-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.analysis-flex-left {
  width: 300px;
  min-width: 220px;
  max-width: 340px;
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.03);
  padding: 12px 8px 12px 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.analysis-flex-right {
  flex: 1;
  min-width: 0;
  padding-left: 18px;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.chart-container {
  width: 100%;
  min-height: 180px;
  flex: 1 1 0%;
}
.el-table.flex-half-table {
  flex: 1 1 0%;
  min-height: 180px;
}

/* 自定义树节点样式 */
.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.checkbox-container {
  margin-right: 8px;
}

/* 确保树节点内容对齐 */
:deep(.el-tree-node__content) {
  padding-right: 0;
}
</style>
